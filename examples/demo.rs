use serde::Deserialize;
use serde_ignored;
use serde_yaml::Value;
use std::fs;
use std::path::Path;

#[derive(Debug, Deserialize)]
pub struct Config {
    pub repos: Vec<Repo>,
    pub minimum_pre_commit_version: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct Repo {
    pub repo: String,
    pub hooks: Vec<ManifestHook>,
}

#[derive(Debug, Deserialize)]
pub struct ManifestHook {
    pub id: String,
    pub name: String,
    pub entry: String,
    pub language: String,
    #[serde(flatten)]
    pub options: HookOptions,
}

#[derive(Debug, Deserialize, Default)]
pub struct HookOptions {
    pub alias: Option<String>,
    pub files: Option<String>,
    pub exclude: Option<String>,
    pub types: Option<Vec<String>>,
    pub types_or: Option<Vec<String>>,
    pub exclude_types: Option<Vec<String>>,
    pub additional_dependencies: Option<Vec<String>>,
    pub args: Option<Vec<String>>,
    pub always_run: Option<bool>,
    pub fail_fast: Option<bool>,
    pub pass_filenames: Option<bool>,
    pub description: Option<String>,
    pub language_version: Option<String>,
    pub log_file: Option<String>,
    pub require_serial: Option<bool>,
    pub stages: Option<Vec<String>>,
    pub verbose: Option<bool>,
    pub minimum_prek_version: Option<String>,
}
pub fn read_config(path: &Path) -> Config {
    let content = fs::read_to_string(path).expect("Failed to read file");
    let value: serde_yaml::Value = serde_yaml::from_str(&content).expect("YAML parse error");

    let mut unused = Vec::new();

    // 先捕获顶层未知字段
    let _config: Config = serde_ignored::deserialize(&value, |path| {
        let key = path.to_string();
        unused.push(key);
    }).expect("Failed to deserialize Config");

    // 遍历 repos -> hooks 捕获每个 hook 的未知字段
    if let serde_yaml::Value::Mapping(mapping) = &value {
        if let Some(repos_val) = mapping.get(&serde_yaml::Value::String("repos".into())) {
            if let serde_yaml::Value::Sequence(repos) = repos_val {
                for (repo_index, repo_val) in repos.iter().enumerate() {
                    if let serde_yaml::Value::Mapping(repo_map) = repo_val {
                        if let Some(hooks_val) = repo_map.get(&serde_yaml::Value::String("hooks".into())) {
                            if let serde_yaml::Value::Sequence(hooks) = hooks_val {
                                for (hook_index, hook_val) in hooks.iter().enumerate() {
                                    // 获取 hook id 用于打印完整路径
                                    let hook_id = hook_val
                                        .get(&serde_yaml::Value::String("id".into()))
                                        .and_then(|v| v.as_str())
                                        .unwrap_or("<unknown>");

                                    let _hook: serde_yaml::Value = serde_ignored::deserialize(
                                        hook_val.clone(),
                                        |path| {
                                            let full_path = format!("hook({}).{}", hook_id, path);
                                            unused.push(full_path);
                                        },
                                    ).expect("Failed to deserialize hook");
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    if !unused.is_empty() {
        println!("Unused keys in Config: {:?}", unused);
    }

    // 最终反序列化成 Config
    serde_yaml::from_str(&content).expect("Failed to deserialize final Config")
}

// ---------- 示例 ----------
fn main() {
    let config = read_config(Path::new("config.yaml"));
    println!("Config: {:#?}", config);
}